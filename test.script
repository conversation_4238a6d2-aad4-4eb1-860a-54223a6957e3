//@version=5
indicator("交易对对比工具", shorttitle="对比", overlay=false)

// 输入参数
compare_symbol = input.symbol("NASDAQ:AAPL", title="对比交易对")

// 获取对比交易对的数据
compare_close = request.security(compare_symbol, timeframe.period, close)

// 声明变量
var float base_price = na
var float compare_base_price = na

// 正确的if语句语法 - 方法1：多行格式
if barstate.isfirst
  base_price := close
  compare_base_price := compare_close

// 或者使用方法2：单行格式（注释掉，因为上面已经处理了）
// if barstate.isfirst => (base_price := close, compare_base_price := compare_close)

// 计算百分比变化
current_pct = (close / base_price - 1) * 100
compare_pct = (compare_close / compare_base_price - 1) * 100

// 绘制图表
plot(current_pct, title="当前交易对", color=color.orange, linewidth=2)
plot(compare_pct, title="对比交易对", color=color.blue, linewidth=2)

// 零轴线
hline(0, title="零轴", color=color.gray, linestyle=hline.style_dashed)