//@version=5
indicator("交易对对比工具", shorttitle="对比", overlay=true)

// 输入参数
compare_symbol = input.symbol("BINANCE:ETHUSDT.P", title="对比交易对")
base_bar = input.int(0, title="基准K线位置 (0=当前K线)", minval=-500, maxval=500)
show_percentage = input.bool(true, title="显示百分比标签")

// 获取对比交易对的数据
compare_close = request.security(compare_symbol, timeframe.period, close, lookahead=barmerge.lookahead_off)

// 获取基准价格（基于用户指定的K线位置）
base_price = close[math.abs(base_bar)]
compare_base_price = compare_close[math.abs(base_bar)]

// 计算相对于基准点的价格变化比例
current_ratio = na(base_price) or base_price == 0 ? na : close / base_price
compare_ratio = na(compare_base_price) or compare_base_price == 0 ? na : compare_close / compare_base_price

// 将比例转换为可视化的价格（以当前交易对的基准价格为基础）
normalized_current = na(current_ratio) ? na : base_price * current_ratio
normalized_compare = na(compare_ratio) ? na : base_price * compare_ratio

// 绘制标准化后的价格线
plot(normalized_current, title="当前交易对", color=color.orange, linewidth=2)
plot(normalized_compare, title="对比交易对", color=color.blue, linewidth=2)

// 在基准K线位置画垂直线
if bar_index == bar_index - math.abs(base_bar)
    line.new(bar_index, low * 0.95, bar_index, high * 1.05, color=color.gray, style=line.style_dashed, width=1)

// 显示百分比变化标签
if show_percentage and barstate.islast
    current_pct = na(current_ratio) ? na : (current_ratio - 1) * 100
    compare_pct = na(compare_ratio) ? na : (compare_ratio - 1) * 100

    if not na(current_pct)
        label.new(bar_index, normalized_current,
                 text="当前: " + str.tostring(current_pct, "#.##") + "%",
                 color=color.orange, textcolor=color.white, size=size.small)

    if not na(compare_pct)
        label.new(bar_index, normalized_compare,
                 text="对比: " + str.tostring(compare_pct, "#.##") + "%",
                 color=color.blue, textcolor=color.white, size=size.small)