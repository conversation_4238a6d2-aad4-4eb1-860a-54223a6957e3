//@version=5
indicator("交易对对比工具", shorttitle="对比", overlay=false)

// 输入参数
compare_symbol = input.symbol("BINANCE:ETHUSDT.P", title="对比交易对")
base_bars_back = input.int(50, title="基准时刻回溯K线数", minval=1, maxval=500)
show_labels = input.bool(true, title="显示百分比标签")

// 获取对比交易对的数据
compare_close = request.security(compare_symbol, timeframe.period, close, lookahead=barmerge.lookahead_off)

// 获取基准时刻的价格（回溯指定K线数）
base_price_current = close[base_bars_back]
base_price_compare = compare_close[base_bars_back]

// 计算从基准时刻开始的百分比变化
current_pct_change = na(base_price_current) or base_price_current == 0 ? na :
                    (close / base_price_current - 1) * 100

compare_pct_change = na(base_price_compare) or base_price_compare == 0 ? na :
                    (compare_close / base_price_compare - 1) * 100

// 绘制百分比变化曲线
plot(current_pct_change, title="当前交易对变化%", color=color.orange, linewidth=2)
plot(compare_pct_change, title="对比交易对变化%", color=color.blue, linewidth=2)

// 零轴基准线
hline(0, title="基准线(0%)", color=color.gray, linestyle=hline.style_dashed)

// 添加网格线
hline(10, title="+10%", color=color.gray, linestyle=hline.style_dotted)
hline(-10, title="-10%", color=color.gray, linestyle=hline.style_dotted)
hline(20, title="+20%", color=color.gray, linestyle=hline.style_dotted)
hline(-20, title="-20%", color=color.gray, linestyle=hline.style_dotted)

// 显示当前数值标签
if show_labels and barstate.islast
    if not na(current_pct_change)
        label.new(bar_index, current_pct_change,
                 text=syminfo.ticker + ": " + str.tostring(current_pct_change, "#.##") + "%",
                 color=color.orange, textcolor=color.white, size=size.small,
                 style=label.style_label_left)

    if not na(compare_pct_change)
        label.new(bar_index, compare_pct_change,
                 text=str.tostring(compare_symbol) + ": " + str.tostring(compare_pct_change, "#.##") + "%",
                 color=color.blue, textcolor=color.white, size=size.small,
                 style=label.style_label_left)

// 在图表上显示基准时刻信息
if barstate.islast and show_labels
    var label info_label = na
    label.delete(info_label)
    info_label := label.new(bar_index - base_bars_back/2,
                           math.max(na(current_pct_change) ? 0 : current_pct_change,
                                   na(compare_pct_change) ? 0 : compare_pct_change) + 5,
                           text="基准时刻: " + str.tostring(base_bars_back) + "根K线前",
                           color=color.yellow, textcolor=color.black, size=size.normal)